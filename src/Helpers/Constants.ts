import { PATHS } from '@Config/Path.Config';
export const API_PATHS = {
  //COUNTS
  LOGIN: 'users/auth/login/',
  VERIFY_OTP: 'users/auth/verify-otp/',
  RESEND_OTP: 'users/auth/resend-otp/',
  REGISTER: 'users/auth/signup/',
  LOGOUT: 'users/auth/logout/',
  DELETE_ACCOUNT: 'users/delete/',
  USER_PROFILE: 'users/profile/',
  NEW_REFRESH_TOKEN: 'cms/auth/refresh-token/',

  COUNTRIES_LIST: 'address/countries/',
  STATES_LIST: 'address/countries/',
  TRADTE_LIST: 'users/trades/',
  ABOUT_US: 'users/static-page/about-us/',
  TERMS_AND_CONDITION: 'users/static-page/terms-conditions/',
  PRIVACY_POLICY: 'users/static-page/privacy-policy/',
  CONTACT_US: 'users/contact-us/',
  NOTIFICATION_SETTINGS: 'users/notifications/preferences/',

  CHAT_SESSION_HISTORY: 'rag/chat-history',
  CHAT_SESSION_DETAILS: 'rag/chat-detail',
  RECENT_SEARCH: 'rag/recent-five-searches',
  QUICK_CALC_SEARCH: 'rag/quick-calc-history/',
  QUICK_CALC_CHAT_SESSION_DETAILS: 'rag/quick-calc-detail',
  DELETE_CHAT_SESSION: 'rag/chat-sessions/delete/',
  DELETE_CALC_SESSION: 'rag/quick-calc-sessions/delete/',
  DOC_SESSION_HISTORY: 'rag/document-history',
  DOC_SESSION_DETAILS: 'rag/document-detail',
  DELETE_DOC_SESSION: 'rag/document-sessions/delete/',
  USER_EDIT_PROFILE: 'users/edit-profile/',
  GET_SUBSCRIPTION_PLAN: 'payment/product/list/',
  CREATE_STRIPE_SESSION: 'payment/create-stripe-session/',
  CANCEL_SUBSCRIPTION: 'payment/cancel-subscription/',
  FEATURE_LIST: 'cms/features/',
  VIDEO_LISTINGS: 'training-education/category-wise-videos/',
  VIDEO_DETAILS: 'training-education/video-detail/',
  VIDEO_PURCHASE: 'training-education/purchase-video/',
  QUIZ_LISTING: 'training-education/quiz-listing/',
  QUIZ_ANSWERS: 'training-education/answers-listing/',
  QUIZ_SUBMIT: 'training-education/quiz/submit/',

  BUSINESS_DETAILS: 'users/business-details/retrieve/',
  BUSINESS_EDIT: 'users/business-details/',

  GET_FORMS_AND_DOCS_LIST: 'form-document/forms-list/1/',
  GET_FORM_UPDATE_LOGO: 'form-document/logo/',

  //BUILDERR"S HUB
  GET_POST_LIST: 'builder-hub/posts/',
  GET_COMMENT_LIST: 'builder-hub/comments/',
  GET_BH_CATEGORY_LIST: 'builder-hub/categories/',
  POST_COMMENT: 'builder-hub/comments/',
  UPDATE_COMMENT: 'builder-hub/comments/',
  POST_REPLY: 'builder-hub/reply/',
  ADD_POST: 'builder-hub/posts/',
  DELETE_POST: 'builder-hub/posts/',
  GET_REPLIES: 'builder-hub/reply/',
  UPDATE_REPLY: 'builder-hub/reply/',
  DELETE_COMMENT: 'builder-hub/comments/',
  DELETE_REPLY: 'builder-hub/reply/',
  GET_REPORT_REASON_LIST: 'builder-hub/report/reasons/',
  CREATE_REPORT: 'builder-hub/report/',
  GET_POST_DETAILS: 'builder-hub/post/detail/',

  //NOTIFICATION_SETTINGS
  NOTIFICATION_LIST: 'users/notifications/',
  NOTIFICATION_READ_ALL: 'users/notifications/mark-all-read/',
  NOTIFICATION_DELETE_ALL: 'users/notifications/delete-all/',
  NOTIFICATION: 'users/notifications/',

  //Analytic Data
  GET_UPLOAD_COUNT: 'analytics-data/get-document-upload-count/?time_range=',
  GET_CATEGORY_WISE_QUESTION_COUNT:
    'analytics-data/get-category-wise-questions-count/?time_range=',
  GET_CATEGORY_WISE_ISSUES_COUNT:
    'analytics-data/get-category-wise-issues-count/?time_range=',

  GET_RF_WISE_ISSUES_COUNT:
    'analytics-data/get-rectification-wise-issues-count/?time_range=',
};

export const MODULE_KEY = {
  ADMINS: 'admin-management',
  KNOWLEDGEBASE: 'knowledge-base',
  USERS: 'user-management',
  CONTACT_REQUEST: 'contact-request',
  LICENCE_APPROVAL: 'licence-approval',
  STATIC_PAGES: 'static-pages',
  ENTERPRISE: 'enterprise-management',
  SUBSCRIPTION: 'subscription-management',
  NOTIFICATION: 'notification-templates',
  TRADIE_HUB: 'tradie-hub',
};

export const MODULES_PATHS: Record<string, string> = {
  [PATHS.HISTORY]: 'histroy',
  [PATHS.HOME]: 'home',
  [PATHS.TRADIE_HUB]: 'tradie-hub',
  [PATHS.QUICK_CALC]: 'quick-calc',
};
