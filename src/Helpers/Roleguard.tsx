import { Navigate, Outlet, useLocation } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { privateRoutes } from '../Config/Routes.Config';
import { PATHS } from '@Config/Path.Config';

interface RoleGuardProps {
  redirectPath?: string;
  isPublicRoute?: boolean;
}

export const RoleGuard: React.FC<RoleGuardProps> = ({
  redirectPath = '/login',
  isPublicRoute = false,
}) => {
  const location = useLocation();
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const userData = useSelector((state: any) => state.UserControle.user);

  if (isPublicRoute && userData?.access_token) {
    return (
      <Navigate
        to={
          userData?.is_subscribed
            ? PATHS.HOME
            : userData?.is_subscription_cancel
              ? PATHS.MY_SUBSCRIPTION
              : userData?.role === 'user'
                ? PATHS.SUBSCRIBE
                : PATHS.HOME
        }
        replace
      />
    );
  }

  if (!isPublicRoute && !userData?.access_token) {
    return <Navigate to={redirectPath} replace />;
  }

  if (!isPublicRoute && userData?.access_token) {
    if (location?.pathname === PATHS.SUBSCRIBE && userData?.is_subscribed)
      return <Navigate to={PATHS.HOME} replace />;

    if (
      location?.pathname === PATHS.SUBSCRIBE &&
      !userData?.is_subscribed &&
      userData?.is_subscription_cancel
    )
      return <Navigate to={PATHS.HOME} replace />;

    if (
      ![PATHS.SUBSCRIBE, PATHS.HOME].includes(location?.pathname) &&
      !userData?.is_subscribed &&
      !userData?.is_subscription_cancel &&
      userData?.role === 'user'
    ) {
      return <Navigate to={PATHS.SUBSCRIBE} replace />;
    }

  }

  return <Outlet />;
};
