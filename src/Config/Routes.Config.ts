import { lazy } from 'react';
import { PATHS } from './Path.Config';

const Login = lazy(() => import('../Pages/Login'));
const Register = lazy(() => import('../Pages/Register'));
const VerifyOtp = lazy(() => import('../Pages/VerifyOtp'));
const Home = lazy(() => import('../Pages/Home'));
const QuickCalc = lazy(() => import('../Pages/QuickCalc'));
const Profile = lazy(() => import('../Pages/Profile'));
const BusinessDetails = lazy(() => import('../Pages/BusinessDetails'));
const AboutUs = lazy(() => import('../Pages/AboutUs'));
const ContactUs = lazy(() => import('../Pages/ContactUs'));
const PrivacyPolicy = lazy(() => import('../Pages/PrivacyPolicy'));
const TermsConditions = lazy(() => import('../Pages/TermsConditions'));
const NotificationSettings = lazy(
  () => import('../Pages/NotificationSettings')
);
const Notifications = lazy(() => import('../Pages/Notifications'));
const History = lazy(() => import('../Pages/History'));
const FormsAndDocuments = lazy(() => import('../Pages/Forms&Documents'));
const TrainingAndEducation = lazy(
  () => import('../Pages/TrainingAndEducation')
);
const ViewAllVideos = lazy(
  () => import('../Pages/TrainingAndEducation/CategoryVideo')
);
const watchVideo = lazy(
  () => import('../Pages/TrainingAndEducation/WatchVideo')
);
const QuizPage = lazy(() => import('../Pages/TrainingAndEducation/QuizPage'));
const ResultPage = lazy(
  () => import('../Pages/TrainingAndEducation/ResultPage')
);
const DetailofDocuments = lazy(
  () => import('../Pages/Forms&Documents/DetailsOfDocument')
);
const MySubscription = lazy(() => import('../Pages/MySubscription'));
const Legal = lazy(() => import('../Pages/PerformanceSolution'));
const BuilderHub = lazy(() => import('../Pages/BuilderHub'));
const Analytics = lazy(() => import('../Pages/Analytics'));
const SinglePost = lazy(() => import('../Pages/BuilderHub/SinglePost'));

export const publicRoutes = [
  {
    path: PATHS.LOGIN,
    element: Login,
  },
  {
    path: PATHS.SIGNUP,
    element: Register,
  },
  {
    path: PATHS.VERIFY_OTP,
    element: VerifyOtp,
  },
];

export const privateRoutes = [
  {
    path: PATHS.HOME,
    element: Home,
  },
  {
    path: PATHS.HISTORY,
    element: History,
  },
  {
    path: PATHS.QUICK_CALC,
    element: QuickCalc,
  },
  {
    path: PATHS.DETAILOFDOC,
    element: DetailofDocuments,
  },
  {
    path: PATHS.LEGALS,
    element: Legal,
  },
  {
    path: PATHS.TRADIE_HUB,
    element: BuilderHub,
  },
  {
    path: `${PATHS.TRADIE_HUB}/:id`,
    element: SinglePost,
  },
  {
    path: PATHS.NOTIFICATION,
    element: Notifications,
  },
  {
    path: PATHS.ANALYTICS,
    element: Analytics,
  },
  {
    path: PATHS.TRAINING,
    element: TrainingAndEducation,
  },
  {
    path: PATHS.QUIZ,
    element: QuizPage,
  },
  {
    path: PATHS.RESULT,
    element: ResultPage,
  },
  {
    path: PATHS.TRAINING_VIDEOS,
    element: ViewAllVideos,
  },
  {
    path: PATHS.WATCH_VIDEO,
    element: watchVideo,
  },
  {
    path: PATHS.PROFILE,
    element: Profile,
  },
  {
    path: PATHS.NOTIFICATION_SETTING,
    element: NotificationSettings,
  },
  {
    path: PATHS.ABOUT_US,
    element: AboutUs,
  },
  {
    path: PATHS.TERMS_CONDITIONS,
    element: TermsConditions,
  },
  {
    path: PATHS.PRIVACY_POLICY,
    element: PrivacyPolicy,
  },
  {
    path: PATHS.CONTACT_US,
    element: ContactUs,
  },
  {
    path: PATHS.MY_SUBSCRIPTION,
    element: MySubscription,
  },
  {
    path: PATHS.ENTERPRISE_BUSINESS_DETAILS,
    element: BusinessDetails,
  },
];
